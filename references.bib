@inproceedings{vaswani2017attention,
  author    = {<PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> and et al.},
  title     = {Attention Is All You Need},
  booktitle = {Advances in Neural Information Processing Systems (NeurIPS)},
  pages     = {5998--6008},
  year      = {2017}
}

@book{camacho2007mpc,
  author    = {<PERSON><PERSON> <PERSON><PERSON> and <PERSON><PERSON>},
  title     = {Model Predictive Control},
  edition   = {2},
  publisher = {Springer},
  year      = {2007}
}

@misc{saunders2020progressive,
  author       = {<PERSON><PERSON> and <PERSON>. <PERSON><PERSON> and <PERSON><PERSON>},
  title        = {Progressive Transformers for End-to-End Sign Language Production},
  howpublished = {arXiv preprint arXiv:2004.14874},
  year         = {2020}
}

@misc{saunders2021multi,
  author       = {<PERSON><PERSON> and N. C. <PERSON> and <PERSON><PERSON>},
  title        = {Continuous 3D Multi-Channel Sign Language Production via Mixed Density Networks},
  howpublished = {arXiv preprint arXiv:2103.06982},
  year         = {2021}
}

@misc{xie2023latent,
  author       = {<PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>},
  title        = {Sign Language Production with Latent Motion Transformer},
  howpublished = {arXiv preprint arXiv:2312.12917},
  year         = {2023}
}


@inproceedings{freeman1995gesture,
  author    = {W. T. Freeman and C. D. Weissman},
  title     = {Television Control by Hand Gestures},
  booktitle = {IEEE Intl. Workshop on Automatic Face and Gesture Recognition},
  year      = {1995}
}

@article{li2021stgcn,
  author    = {Y. Li and Z. Zhao and X. Liu and et al.},
  title     = {Spatio-Temporal Graph Routing for Skeleton-based Action Recognition},
  journal   = {IEEE Transactions on Image Processing},
  volume    = {30},
  pages     = {7156--7170},
  year      = {2021}
}

@inproceedings{camgoz2018neural,
  author    = {N. C. Camgoz and O. Koller and H. Ney and R. Bowden},
  title     = {Neural Sign Language Translation},
  booktitle = {CVPR},
  pages     = {7784--7793},
  year      = {2018}
}

@inproceedings{niu2020finegrained,
  author    = {Z. Niu and B. Mak},
  title     = {Stochastic Fine-Grained Labeling of Multi-State Sign Glosses for Continuous Sign Language Recognition},
  booktitle = {ECCV},
  pages     = {294--311},
  year      = {2020}
}

@article{woods2023wlasl,
  author    = {L. Woods and Z. A. Rana},
  title     = {Modeling Sign Language with Encoder-Only Transformers Using WLASL},
  journal   = {Mathematics},
  volume    = {11},
  number    = {9},
  pages     = {2129},
  year      = {2023}
}

@inproceedings{bohacek2022pose,
  author    = {M. Boháček and P. Hruž},
  title     = {Sign Pose-Based Transformer for Word-Level Sign Language Recognition},
  booktitle = {WACV Workshops},
  year      = {2022}
}

@article{kothadiya2022signformer,
  author    = {D. R. Kothadiya and V. N. Dadhania and P. T. Gohil and K. R. Jain},
  title     = {SIGNFORMER: DeepVision Transformer for Indian Sign Language Recognition},
  journal   = {IEEE Access},
  volume    = {10},
  pages     = {119057--119068},
  year      = {2022}
}

@article{meng2021multiscale,
  author    = {L. Meng and X. Chen and J. Zhao and et al.},
  title     = {Attention-Enhanced Multi-Scale Dual Sign Language Recognition Network Based on GCN},
  journal   = {Sensors},
  volume    = {21},
  number    = {3},
  pages     = {903},
  year      = {2021}
}

@article{said2025adaptive,
  author    = {Y. Said and S. Boubaker and S. M. Altowaijri and A. A. Alsheikhy and M. Atri},
  title     = {Adaptive Transformer-Based Deep Learning Framework for Continuous Sign Language Recognition and Translation},
  journal   = {Mathematics},
  volume    = {13},
  number    = {6},
  pages     = {909},
  year      = {2025}
}

@article{huang2019attention,
  author    = {J. Huang and W. Zhou and H. Li},
  title     = {Attention-Based 3D-CNNs for Large-Vocabulary Sign Language Recognition},
  journal   = {IEEE Trans. Circuits Syst. Video Technol.},
  volume    = {29},
  number    = {9},
  pages     = {2822--2832},
  year      = {2019}
}

@inproceedings{pu2019iterative,
  author    = {J. Pu and W. Zhou and H. Li},
  title     = {Iterative Alignment Network for Continuous Sign Language Recognition},
  booktitle = {CVPR},
  pages     = {4164--4173},
  year      = {2019}
}

@inproceedings{zhou2019pseudo,
  author    = {H. Zhou and W. Zhou and H. Li},
  title     = {Dynamic Pseudo Label Decoding for Continuous Sign Language Recognition},
  booktitle = {IEEE Intl. Conf. on Multimedia and Expo (ICME)},
  pages     = {876--881},
  year      = {2019}
}

@inproceedings{koishybay2021fine,
  author    = {K. Koishybay and M. Mukushev and A. Sandygulova},
  title     = {Continuous Sign Language Recognition with Iterative Spatiotemporal Fine-Tuning},
  booktitle = {Proc. 25th ICPR},
  pages     = {4182--4189},
  year      = {2021}
}

@misc{cheng2020fcn,
  author       = {K. L. Cheng and Z. Yang and Q. Chen and Y.-W. Tai},
  title        = {Fully Convolutional Networks for Continuous Sign Language Recognition},
  howpublished = {arXiv preprint arXiv:2007.12402},
  year         = {2020}
}

@article{pan2018keyframe,
  author    = {W. Pan and W. Zhou and H. Li},
  title     = {Attention-Based Sign Language Recognition Network Utilizing Keyframe Sampling and Skeletal Features},
  journal   = {IEEE Access},
  volume    = {6},
  pages     = {56968--56977},
  year      = {2018}
}

@inproceedings{hu2021signbert,
  author    = {H. Hu and W. Zhao and W. Zhou and Y. Wang and H. Li},
  title     = {SignBERT: Pre-training of Hand-Model-Aware Representation for Sign Language Recognition},
  booktitle = {ICCV},
  pages     = {7200--7209},
  year      = {2021}
}

@misc{zhao2023best,
  author       = {W. Zhao and H. Hu and W. Zhou and J. Shi and H. Li},
  title        = {BEST: BERT Pre-Training for Sign Language Recognition with Coupling Tokenization},
  howpublished = {arXiv preprint arXiv:2302.05075},
  year         = {2023}
}

@article{huang2021boundary,
  author    = {S. Huang and Z. Ye},
  title     = {Boundary-Adaptive Encoder With Attention Method for Chinese Sign Language Recognition},
  journal   = {IEEE Access},
  volume    = {9},
  pages     = {70948--70960},
  year      = {2021}
}

@inproceedings{yin2021simulSLT,
  author    = {A. Yin and Z. Zhao and J. Liu and et al.},
  title     = {SimulSLT: End-to-End Simultaneous Sign Language Translation},
  booktitle = {Proc. ACM Multimedia (MM)},
  pages     = {155--164},
  year      = {2021}
}

@inproceedings{camgoz2020slt,
  author    = {N. C. Camgoz and O. Koller and S. Hadfield and R. Bowden},
  title     = {Sign Language Transformers: Joint Sign Language Recognition and Translation},
  booktitle = {CVPR},
  pages     = {10023--10033},
  year      = {2020}
}

@article{koller2015large,
  author    = {O. Koller and H. Ney and R. Bowden},
  title     = {Continuous Sign Language Recognition: Towards Large Vocabulary Statistical Recognition Systems Handling Multiple Signers},
  journal   = {Computer Vision and Image Understanding},
  volume    = {141},
  pages     = {108--125},
  year      = {2015}
}

@inproceedings{shen2024mmwlauslan,
  author    = {X. Shen and H. Du and H. Sheng and et al.},
  title     = {MM-WLAuslan: Multi-View Multi-Modal Word-Level Australian Sign Language Recognition Dataset},
  booktitle = {NeurIPS Datasets \& Benchmarks Track},
  year      = {2024}
}

@misc{zhu2024cecsldataset,
  author       = {Q. Zhu and J. Li and F. Yuan and J. Fan and Q. Gan},
  title        = {A Chinese Continuous Sign Language Dataset Based on Complex Environments (CE-CSL)},
  howpublished = {arXiv preprint arXiv:2409.11960},
  year         = {2024}
}

@misc{li2023twostream,
  author       = {J. Li and Y. Wang and Z. Chen},
  title        = {Chinese Sign Language Recognition Based on Two-Stream CNN and LSTM Network},
  howpublished = {ResearchGate preprint},
  year         = {2023}
}

@misc{li2024motorattention,
  author       = {Q. Li and Y. Wang and Z. Chen},
  title        = {Continuous Chinese Sign Language Recognition Based on Motor Attention},
  howpublished = {arXiv preprint arXiv:2402.19118},
  year         = {2024}
}

