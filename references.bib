@article{alyamiReviewing25Years2024a,
  title = {Reviewing 25 Years of Continuous Sign Language Recognition Research: Advances, Challenges, and Prospects},
  author = {<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>, <PERSON>},
  year = {2024},
  journal = {Information Processing \& Management},
  volume = {61},
  number = {5},
  pages = {103774},
  issn = {0306-4573},
  doi = {10.1016/j.ipm.2024.103774}
}


@article{jiangSurveyChineseSign2024,
  title = {A Survey on Chinese Sign Language Recognition: From Traditional Methods to Artificial Intelligence},
  author = {<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON>},
  year = {2024},
  journal = {CMES - Computer Modeling in Engineering and Sciences},
  volume = {140},
  number = {1},
  pages = {1--40},
  issn = {1526-1492},
  doi = {10.32604/cmes.2024.047649}
}

@article{zhangRecentAdvancesDeep2024,
  title = {Recent Advances on Deep Learning for Sign Language Recognition},
  author = {<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON>},
  year = {2024},
  journal = {CMES - Computer Modeling in Engineering and Sciences},
  volume = {139},
  number = {3},
  pages = {2399--2450},
  issn = {1526-1492},
  doi = {10.32604/cmes.2023.045731}
}

@inproceedings{HuangVideobased2018,
  title = {Video-based sign language recognition without temporal segmentation},
  author = {Huang, Jie and Zhou, Wengang and Zhang, Qilin and Li, Houqiang and Li, Weiping},
  year = {2018},
  isbn = {978-1-57735-800-8},
  publisher = {AAAI Press},
  articleno = {275},
  numpages = {8},
  location = {New Orleans, Louisiana, USA},
}

@inproceedings{cuiRecurrentConvolutionalNeural2017,
  title = {Recurrent Convolutional Neural Networks for Continuous Sign Language Recognition by Staged Optimization},
  booktitle = {2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)},
  author = {{R. Cui} and {H. Liu} and {C. Zhang}},
  year = {2017},
  month = jul,
  pages = {1610--1618},
  doi = {10.1109/CVPR.2017.175}
}


@article{kollerContinuousSignLanguage2015,
  title = {Continuous Sign Language Recognition: Towards Large Vocabulary Statistical Recognition Systems Handling Multiple Signers},
  author = {Koller, Oscar and Forster, Jens and Ney, Hermann},
  year = {2015},
  month = dec,
  journal = {Pose \& Gesture},
  volume = {141},
  pages = {108--125},
  issn = {1077-3142},
  doi = {10.1016/j.cviu.2015.09.013}
}

@inproceedings{cihancamgozSignLanguageTransformers2020,
  title = {Sign Language Transformers: Joint End-to-End Sign Language Recognition and Translation},
  booktitle = {2020 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  author = {Cihan Camgoz, Necati and Koller, Oscar and Hadfield, Simon and Bowden, Richard},
  year = {2020},
  month = jun,
  pages = {10020--10030},
  publisher = {IEEE},
  doi = {10.1109/CVPR42600.2020.01004}
}
@inproceedings{liWordlevelDeepSign2020a,
  title = {Word-Level Deep Sign Language Recognition from Video: A New Large-Scale Dataset and Methods Comparison},
  booktitle = {2020 IEEE Winter Conference on Applications of Computer Vision (WACV)},
  author = {Li, Dongxu and Opazo, Cristian Rodriguez and Yu, Xin and Li, Hongdong},
  year = {2020},
  month = mar,
  pages = {1448--1458},
  publisher = {IEEE},
  doi = {10.1109/WACV45572.2020.9093512}
}
@inproceedings{chenBiLSTMCTCBased2024,
  title = {A BiLSTM and CTC Based Multi-Sensor Information Fusion Frame for Continuous Sign Language Recognition},
  booktitle = {2024 10th International Conference on Electrical Engineering, Control and Robotics (EECR)},
  author = {{Y. Chen} and {J. Li} and {S. Lin} and {Y. Xu} and {C. Yang}},
  year = {2024},
  month = mar,
  pages = {310--315},
  doi = {10.1109/EECR60807.2024.10607314}
}

@misc{zuoImprovingContinuousSign2024,
  title = {Improving Continuous Sign Language Recognition with Consistency Constraints and Signer Removal},
  author = {Zuo, Ronglai and Mak, Brian},
  year = {2024},
  month = jan,
  number = {arXiv:2212.13023},
  eprint = {2212.13023},
  primaryclass = {cs},
  publisher = {arXiv},
  doi = {10.48550/arXiv.2212.13023},
  archiveprefix = {arXiv}
}



@misc{zhuMultiscaleTemporalNetwork2022,
  title = {Multi-Scale Temporal Network for Continuous Sign Language Recognition},
  author = {Zhu, Qidan and Li, Jing and Yuan, Fei and Gan, Quan},
  year = {2022},
  month = aug,
  number = {arXiv:2204.03864},
  eprint = {2204.03864},
  primaryclass = {cs},
  publisher = {arXiv},
  doi = {10.48550/arXiv.2204.03864},
  archiveprefix = {arXiv}
}

@inproceedings{ahnSlowfastNetworkContinuous2024,
  title = {Slowfast Network for Continuous Sign Language Recognition},
  booktitle = {ICASSP 2024 - 2024 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)},
  author = {{J. Ahn} and {Y. Jang} and {J. S. Chung}},
  year = {2024},
  month = apr,
  pages = {3920--3924},
  doi = {10.1109/ICASSP48485.2024.10445841}
}

@inproceedings{huTransRACEncodingMultiscale2022,
  title = {TransRAC: Encoding Multi-Scale Temporal Correlation with Transformers for Repetitive Action Counting},
  booktitle = {2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  author = {Hu, Huazhang and Dong, Sixun and Zhao, Yiqun and Lian, Dongze and Li, Zhengxin and Gao, Shenghua},
  year = {2022},
  month = jun,
  pages = {18991--19000},
  publisher = {IEEE},
  doi = {10.1109/CVPR52688.2022.01843}
}


@article{hanTSRTVDTemporalSuperresolution2020,
  title = {TSR-TVD: Temporal Super-Resolution for Time-Varying Data Analysis and Visualization},
  author = {{J. Han} and {C. Wang}},
  year = {2020},
  month = jan,
  journal = {IEEE Transactions on Visualization and Computer Graphics},
  volume = {26},
  number = {1},
  pages = {205--215},
  issn = {1941-0506},
  doi = {10.1109/TVCG.2019.2934255}
}
@article{xieMultiscaleLocaltemporalSimilarity2023,
  title = {Multi-Scale Local-Temporal Similarity Fusion for Continuous Sign Language Recognition},
  author = {Xie, Pan and Cui, Zhi and Du, Yao and Zhao, Mengyi and Cui, Jianwei and Wang, Bin and Hu, Xiaohui},
  year = {2023},
  journal = {Pattern Recognition},
  volume = {136},
  pages = {109233},
  issn = {0031-3203},
  doi = {10.1016/j.patcog.2022.109233}
}

@inproceedings{minVisualAlignmentConstraint2021a,
  title = {Visual Alignment Constraint for Continuous Sign Language Recognition},
  booktitle = {2021 IEEE/CVF International Conference on Computer Vision (ICCV)},
  author = {Min, Yuecong and Hao, Aiming and Chai, Xiujuan and Chen, Xilin},
  year = {2021},
  month = oct,
  pages = {11522--11531},
  publisher = {IEEE},
  doi = {10.1109/ICCV48922.2021.01134}
}

@inproceedings{huContinuousSignLanguage2023a,
  title = {Continuous Sign Language Recognition with Correlation Network},
  booktitle = {2023 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  author = {Hu, Lianyu and Gao, Liqing and Liu, Zekang and Feng, Wei},
  year = {2023},
  month = jun,
  pages = {2529--2539},
  publisher = {IEEE},
  doi = {10.1109/CVPR52729.2023.00249}
}
@misc{huCorrNetSignLanguage2024a,
  title = {CorrNet+: Sign Language Recognition and Translation via Spatial-Temporal Correlation},
  author = {Hu, Lianyu and Feng, Wei and Gao, Liqing and Liu, Zekang and Wan, Liang},
  year = {2024},
  month = apr,
  number = {arXiv:2404.11111},
  eprint = {2404.11111},
  primaryclass = {cs},
  publisher = {arXiv},
  doi = {10.48550/arXiv.2404.11111},
  archiveprefix = {arXiv}
}
@article{zhouSpatialtemporalMulticueNetwork2022,
  title = {Spatial-Temporal Multi-Cue Network for Sign Language Recognition and Translation},
  author = {{H. Zhou} and {W. Zhou} and {Y. Zhou} and {H. Li}},
  year = {2022},
  journal = {IEEE Transactions on Multimedia},
  volume = {24},
  pages = {768--779},
  issn = {1941-0077},
  doi = {10.1109/TMM.2021.3059098}
}
@inproceedings{zuoC2SLRConsistencyenhancedContinuous2022,
  title = {C2SLR: Consistency-Enhanced Continuous Sign Language Recognition},
  booktitle = {2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  author = {{R. Zuo} and {B. Mak}},
  year = {2022},
  month = jun,
  pages = {5121--5130},
  doi = {10.1109/CVPR52688.2022.00507}
}
@InProceedings{Alyami_2025_CVPR,
    author    = {Alyami, Sarah and Luqman, Hamzah},
    title     = {CLIP-SLA: Parameter-Efficient CLIP Adaptation for Continuous Sign Language Recognition},
    booktitle = {Proceedings of the Computer Vision and Pattern Recognition Conference (CVPR) Workshops},
    month     = {June},
    year      = {2025},
    pages     = {4098-4108}
}
@inproceedings{dinaiTAPSTemporalAttentionbased2025,
  title = {TAPS: Temporal Attention-Based Pruning and~Scaling for~Efficient Video Action Recognition},
  booktitle = {Computer Vision - ACCV 2024},
  author = {Dinai, Yonatan and Raviv, Avraham and Harel, Nimrod and Kim, Donghoon and Goldin, Ishay and Zehngut, Niv},
  year = {2025},
  pages = {422--438},
  publisher = {Springer Nature Singapore}
}

@article{KOLLER2015108,
title = {Continuous sign language recognition: Towards large vocabulary statistical recognition systems handling multiple signers [dataset]},
journal = {Computer Vision and Image Understanding},
volume = {141},
pages = {108-125},
year = {2015},
issn = {1077-3142},
doi = {https://doi.org/10.1016/j.cviu.2015.09.013},
author = {Oscar Koller and Jens Forster and Hermann Ney},
}

@INPROCEEDINGS{8578910,
  author={Camgoz, Necati Cihan and Hadfield, Simon and Koller, Oscar and Ney, Hermann and Bowden, Richard},
  booktitle={2018 IEEE/CVF Conference on Computer Vision and Pattern Recognition}, 
  title={Neural Sign Language Translation [dataset]}, 
  year={2018},
  volume={},
  number={},
  pages={7784-7793},
  doi={10.1109/CVPR.2018.00812}
}

@inproceedings{zhou2021,
  author    = {Hao Zhou and Wen-gang Zhou and Weizhen Qi and Junfu Pu and Houqiang Li},
  year      = {2021},
  title     = {Improving Sign Language Translation with Monolingual Data by Sign Back-Translation [dataset]},
  booktitle = {Computer Vision and Pattern Recognition},
  doi       = {10.1109/CVPR46437.2021.00137},
}

@inproceedings{huSqueezeandexcitationNetworks2018,
  title = {Squeeze-and-Excitation Networks},
  booktitle = {2018 IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  author = {{J. Hu} and {L. Shen} and {G. Sun}},
  year = {2018},
  month = jun,
  pages = {7132--7141},
  doi = {10.1109/CVPR.2018.00745}
}
@inproceedings{wooCBAMConvolutionalBlock2018,
  title = {CBAM: Convolutional Block Attention Module},
  booktitle = {Computer Vision - ECCV 2018},
  author = {Woo, Sanghyun and Park, Jongchan and Lee, Joon-Young and Kweon, In So},
  year = {2018},
  pages = {3--19},
  publisher = {Springer International Publishing}
}
@inproceedings{wangNonlocalNeuralNetworks2018,
  title = {Non-Local Neural Networks},
  booktitle = {2018 IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  author = {{X. Wang} and {R. Girshick} and {A. Gupta} and {K. He}},
  year = {2018},
  month = jun,
  pages = {7794--7803},
  doi = {10.1109/CVPR.2018.00813}
}
@inproceedings{carreiraQuoVadisAction2017,
  title = {Quo Vadis, Action Recognition? A New Model and the Kinetics Dataset},
  booktitle = {2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)},
  author = {Carreira, Joao and Zisserman, Andrew},
  year = {2017},
  month = jul,
  pages = {4724--4733},
  publisher = {IEEE},
  doi = {10.1109/CVPR.2017.502}
}
@inproceedings{tranCloserLookSpatiotemporal2018,
  title = {A Closer Look at Spatiotemporal Convolutions for Action Recognition},
  booktitle = {2018 IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  author = {Tran, Du and Wang, Heng and Torresani, Lorenzo and Ray, Jamie and LeCun, Yann and Paluri, Manohar},
  year = {2018},
  month = jun,
  pages = {6450--6459},
  publisher = {IEEE},
  doi = {10.1109/CVPR.2018.00675}
}
@inproceedings{linTSMTemporalShift2019,
  title = {TSM: Temporal Shift Module for Efficient Video Understanding},
  booktitle = {2019 IEEE/CVF International Conference on Computer Vision (ICCV)},
  author = {Lin, Ji and Gan, Chuang and Han, Song},
  year = {2019},
  month = oct,
  pages = {7082--7092},
  publisher = {IEEE},
  doi = {10.1109/ICCV.2019.00718}
}
@incollection{niuStochasticFinegrainedLabeling2020,
  title = {Stochastic Fine-Grained Labeling of Multi-State Sign Glosses for Continuous Sign Language Recognition},
  booktitle = {Computer Vision - ECCV 2020},
  author = {Niu, Zhe and Mak, Brian},
  year = {2020},
  volume = {12361},
  pages = {172--186},
  publisher = {Springer International Publishing},
  doi = {10.1007/978-3-030-58517-4\_11}
}
@incollection{chengFullyConvolutionalNetworks2020,
  title = {Fully Convolutional Networks for Continuous Sign Language Recognition},
  author = {Cheng, Ka Leong and Yang, Zhaoyang and Chen, Qifeng and Tai, Yu-Wing},
  year = {2020},
  volume = {12369},
  eprint = {2007.12402},
  primaryclass = {cs},
  pages = {697--714},
  doi = {10.1007/978-3-030-58586-0\_41},
  archiveprefix = {arXiv}
}
@inproceedings{puBoostingContinuousSign2020,
  title = {Boosting Continuous Sign Language Recognition via Cross Modality Augmentation},
  booktitle = {Proceedings of the 28th ACM International Conference on Multimedia},
  author = {Pu, Junfu and Zhou, Wengang and Hu, Hezhen and Li, Houqiang},
  year = {2020},
  month = oct,
  eprint = {2010.05264},
  primaryclass = {cs},
  pages = {1497--1505},
  doi = {10.1145/3394171.3413931},
  archiveprefix = {arXiv}
}
@inproceedings{haoSelfmutualDistillationLearning2021,
  title = {Self-Mutual Distillation Learning for Continuous Sign Language Recognition},
  booktitle = {2021 IEEE/CVF International Conference on Computer Vision (ICCV)},
  author = {Hao, Aiming and Min, Yuecong and Chen, Xilin},
  year = {2021},
  month = oct,
  pages = {11283--11292},
  publisher = {IEEE},
  doi = {10.1109/ICCV48922.2021.01111}
}
@incollection{huTemporalLiftPooling2022a,
  title = {Temporal Lift Pooling for Continuous Sign Language Recognition},
  booktitle = {Computer Vision - ECCV 2022},
  author = {Hu, Lianyu and Gao, Liqing and Liu, Zekang and Feng, Wei},
  year = {2022},
  volume = {13695},
  pages = {511--527},
  publisher = {Springer Nature Switzerland},
  doi = {10.1007/978-3-031-19833-5\_30}
}
@article{huSelfemphasizingNetworkContinuous2023,
  title = {Self-Emphasizing Network for Continuous Sign Language Recognition},
  author = {Hu, Lianyu and Gao, Liqing and Liu, Zekang and Feng, Wei},
  year = {2023},
  month = jun,
  journal = {Proceedings of the AAAI Conference on Artificial Intelligence},
  volume = {37},
  number = {1},
  pages = {854--862},
  issn = {2374-3468, 2159-5399},
  doi = {10.1609/aaai.v37i1.25164}
}

@inproceedings{guoDistillingCrosstemporalContexts2023,
  title = {Distilling Cross-Temporal Contexts for Continuous Sign Language Recognition},
  booktitle = {2023 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)},
  author = {{L. Guo} and {W. Xue} and {Q. Guo} and {B. Liu} and {K. Zhang} and {T. Yuan} and {S. Chen}},
  year = {2023},
  month = jun,
  pages = {10771--10780},
  doi = {10.1109/CVPR52729.2023.01037}
}

@article{kollerWeaklySupervisedLearning2020,
  title = {Weakly Supervised Learning with Multi-Stream CNN-LSTM-HMMs to Discover Sequential Parallelism in Sign Language Videos},
  author = {{O. Koller} and {N. C. Camgoz} and {H. Ney} and {R. Bowden}},
  year = {2020},
  month = sep,
  journal = {IEEE Transactions on Pattern Analysis and Machine Intelligence},
  volume = {42},
  number = {9},
  pages = {2306--2320},
  issn = {1939-3539},
  doi = {10.1109/TPAMI.2019.2911077}
}

@article{cuiDeepNeuralFramework2019,
  title = {A Deep Neural Framework for Continuous Sign Language Recognition by Iterative Training},
  author = {{R. Cui} and {H. Liu} and {C. Zhang}},
  year = {2019},
  month = jul,
  journal = {IEEE Transactions on Multimedia},
  volume = {21},
  number = {7},
  pages = {1880--1891},
  issn = {1941-0077},
  doi = {10.1109/TMM.2018.2889563}
}



@article{wangContinuousSignLanguage2025,
  title = {Continuous Sign Language Recognition with Multi-Scale Spatial-Temporal Feature Enhancement},
  author = {{Z. Wang} and {D. Li} and {R. Jiang} and {M. Okumura}},
  year = {2025},
  journal = {IEEE Access},
  volume = {13},
  pages = {5491--5506},
  issn = {2169-3536},
  doi = {10.1109/ACCESS.2025.3526330}
}

@article{guanMSKAMultistreamKeypoint2025,
  title = {MSKA: Multi-Stream Keypoint Attention Network for Sign Language Recognition and Translation},
  author = {Guan, Mo and Wang, Yan and Ma, Guangkun and Liu, Jiarui and Sun, Mingzu},
  year = {2025},
  journal = {Pattern Recognition},
  volume = {165},
  pages = {111602},
  issn = {0031-3203},
  doi = {10.1016/j.patcog.2025.111602}
}



@misc{luTCNetContinuousSign2024a,
  title = {TCNet: Continuous Sign Language Recognition from Trajectories and Correlated Regions},
  author = {Lu, Hui and Salah, Albert Ali and Poppe, Ronald},
  year = {2024},
  month = mar,
  number = {arXiv:2403.11818},
  eprint = {2403.11818},
  primaryclass = {cs},
  publisher = {arXiv},
  doi = {10.48550/arXiv.2403.11818},
  archiveprefix = {arXiv}
}

