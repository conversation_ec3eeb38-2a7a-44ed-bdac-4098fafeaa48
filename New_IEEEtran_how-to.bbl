% Generated by IEEEtran.bst, version: 1.14 (2015/08/26)
\begin{thebibliography}{10}
\providecommand{\url}[1]{#1}
\csname url@samestyle\endcsname
\providecommand{\newblock}{\relax}
\providecommand{\bibinfo}[2]{#2}
\providecommand{\BIBentrySTDinterwordspacing}{\spaceskip=0pt\relax}
\providecommand{\BIBentryALTinterwordstretchfactor}{4}
\providecommand{\BIBentryALTinterwordspacing}{\spaceskip=\fontdimen2\font plus
\BIBentryALTinterwordstretchfactor\fontdimen3\font minus \fontdimen4\font\relax}
\providecommand{\BIBforeignlanguage}[2]{{%
\expandafter\ifx\csname l@#1\endcsname\relax
\typeout{** WARNING: IEEEtran.bst: No hyphenation pattern has been}%
\typeout{** loaded for the language `#1'. Using the pattern for}%
\typeout{** the default language instead.}%
\else
\language=\csname l@#1\endcsname
\fi
#2}}
\providecommand{\BIBdecl}{\relax}
\BIBdecl

\bibitem{alyamiReviewing25Years2024a}
S.~<PERSON>yami, H.~Luqman, and M.~Hammoudeh, ``Reviewing 25 years of continuous sign language recognition research: Advances, challenges, and prospects,'' \emph{Information Processing \& Management}, vol.~61, no.~5, p. 103774, 2024.

\bibitem{jiangSurveyChineseSign2024}
X.~Jiang, Y.~Zhang, J.~Lei, and Y.~Zhang, ``A survey on chinese sign language recognition: From traditional methods to artificial intelligence,'' \emph{CMES - Computer Modeling in Engineering and Sciences}, vol. 140, no.~1, pp. 1--40, 2024.

\bibitem{zhangRecentAdvancesDeep2024}
Y.~Zhang and X.~Jiang, ``Recent advances on deep learning for sign language recognition,'' \emph{CMES - Computer Modeling in Engineering and Sciences}, vol. 139, no.~3, pp. 2399--2450, 2024.

\bibitem{HuangVideobased2018}
J.~Huang, W.~Zhou, Q.~Zhang, H.~Li, and W.~Li, ``Video-based sign language recognition without temporal segmentation.''\hskip 1em plus 0.5em minus 0.4em\relax AAAI Press, 2018.

\bibitem{cuiRecurrentConvolutionalNeural2017}
{R. Cui}, {H. Liu}, and {C. Zhang}, ``Recurrent convolutional neural networks for continuous sign language recognition by staged optimization,'' in \emph{2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)}, Jul. 2017, pp. 1610--1618.

\bibitem{kollerContinuousSignLanguage2015}
O.~Koller, J.~Forster, and H.~Ney, ``Continuous sign language recognition: Towards large vocabulary statistical recognition systems handling multiple signers,'' \emph{Pose \& Gesture}, vol. 141, pp. 108--125, Dec. 2015.

\bibitem{cihancamgozSignLanguageTransformers2020}
N.~Cihan~Camgoz, O.~Koller, S.~Hadfield, and R.~Bowden, ``Sign language transformers: Joint end-to-end sign language recognition and translation,'' in \emph{2020 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, Jun. 2020, pp. 10\,020--10\,030.

\bibitem{liWordlevelDeepSign2020a}
D.~Li, C.~R. Opazo, X.~Yu, and H.~Li, ``Word-level deep sign language recognition from video: A new large-scale dataset and methods comparison,'' in \emph{2020 IEEE Winter Conference on Applications of Computer Vision (WACV)}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, Mar. 2020, pp. 1448--1458.

\bibitem{chenBiLSTMCTCBased2024}
{Y. Chen}, {J. Li}, {S. Lin}, {Y. Xu}, and {C. Yang}, ``A bilstm and ctc based multi-sensor information fusion frame for continuous sign language recognition,'' in \emph{2024 10th International Conference on Electrical Engineering, Control and Robotics (EECR)}, Mar. 2024, pp. 310--315.

\bibitem{zuoImprovingContinuousSign2024}
R.~Zuo and B.~Mak, ``Improving continuous sign language recognition with consistency constraints and signer removal,'' Jan. 2024.

\bibitem{zhuMultiscaleTemporalNetwork2022}
Q.~Zhu, J.~Li, F.~Yuan, and Q.~Gan, ``Multi-scale temporal network for continuous sign language recognition,'' Aug. 2022.

\bibitem{ahnSlowfastNetworkContinuous2024}
{J. Ahn}, {Y. Jang}, and {J. S. Chung}, ``Slowfast network for continuous sign language recognition,'' in \emph{ICASSP 2024 - 2024 IEEE International Conference on Acoustics, Speech and Signal Processing (ICASSP)}, Apr. 2024, pp. 3920--3924.

\bibitem{huTransRACEncodingMultiscale2022}
H.~Hu, S.~Dong, Y.~Zhao, D.~Lian, Z.~Li, and S.~Gao, ``Transrac: Encoding multi-scale temporal correlation with transformers for repetitive action counting,'' in \emph{2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, Jun. 2022, pp. 18\,991--19\,000.

\bibitem{hanTSRTVDTemporalSuperresolution2020}
{J. Han} and {C. Wang}, ``Tsr-tvd: Temporal super-resolution for time-varying data analysis and visualization,'' \emph{IEEE Transactions on Visualization and Computer Graphics}, vol.~26, no.~1, pp. 205--215, Jan. 2020.

\bibitem{xieMultiscaleLocaltemporalSimilarity2023}
P.~Xie, Z.~Cui, Y.~Du, M.~Zhao, J.~Cui, B.~Wang, and X.~Hu, ``Multi-scale local-temporal similarity fusion for continuous sign language recognition,'' \emph{Pattern Recognition}, vol. 136, p. 109233, 2023.

\bibitem{minVisualAlignmentConstraint2021a}
Y.~Min, A.~Hao, X.~Chai, and X.~Chen, ``Visual alignment constraint for continuous sign language recognition,'' in \emph{2021 IEEE/CVF International Conference on Computer Vision (ICCV)}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, Oct. 2021, pp. 11\,522--11\,531.

\bibitem{huContinuousSignLanguage2023a}
L.~Hu, L.~Gao, Z.~Liu, and W.~Feng, ``Continuous sign language recognition with correlation network,'' in \emph{2023 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, Jun. 2023, pp. 2529--2539.

\bibitem{huCorrNetSignLanguage2024a}
L.~Hu, W.~Feng, L.~Gao, Z.~Liu, and L.~Wan, ``Corrnet+: Sign language recognition and translation via spatial-temporal correlation,'' Apr. 2024.

\bibitem{zhouSpatialtemporalMulticueNetwork2022}
{H. Zhou}, {W. Zhou}, {Y. Zhou}, and {H. Li}, ``Spatial-temporal multi-cue network for sign language recognition and translation,'' \emph{IEEE Transactions on Multimedia}, vol.~24, pp. 768--779, 2022.

\bibitem{zuoC2SLRConsistencyenhancedContinuous2022}
{R. Zuo} and {B. Mak}, ``C2slr: Consistency-enhanced continuous sign language recognition,'' in \emph{2022 IEEE/CVF Conference on Computer Vision and Pattern Recognition (CVPR)}, Jun. 2022, pp. 5121--5130.

\bibitem{Alyami_2025_CVPR}
S.~Alyami and H.~Luqman, ``Clip-sla: Parameter-efficient clip adaptation for continuous sign language recognition,'' in \emph{Proceedings of the Computer Vision and Pattern Recognition Conference (CVPR) Workshops}, June 2025, pp. 4098--4108.

\bibitem{dinaiTAPSTemporalAttentionbased2025}
Y.~Dinai, A.~Raviv, N.~Harel, D.~Kim, I.~Goldin, and N.~Zehngut, ``Taps: Temporal attention-based pruning and~scaling for~efficient video action recognition,'' in \emph{Computer Vision - ACCV 2024}.\hskip 1em plus 0.5em minus 0.4em\relax Springer Nature Singapore, 2025, pp. 422--438.

\bibitem{KOLLER2015108}
O.~Koller, J.~Forster, and H.~Ney, ``Continuous sign language recognition: Towards large vocabulary statistical recognition systems handling multiple signers [dataset],'' \emph{Computer Vision and Image Understanding}, vol. 141, pp. 108--125, 2015.

\bibitem{8578910}
N.~C. Camgoz, S.~Hadfield, O.~Koller, H.~Ney, and R.~Bowden, ``Neural sign language translation [dataset],'' in \emph{2018 IEEE/CVF Conference on Computer Vision and Pattern Recognition}, 2018, pp. 7784--7793.

\bibitem{zhou2021}
H.~Zhou, W.~gang Zhou, W.~Qi, J.~Pu, and H.~Li, ``Improving sign language translation with monolingual data by sign back-translation [dataset],'' in \emph{Computer Vision and Pattern Recognition}, 2021.

\bibitem{huSqueezeandexcitationNetworks2018}
{J. Hu}, {L. Shen}, and {G. Sun}, ``Squeeze-and-excitation networks,'' in \emph{2018 IEEE/CVF Conference on Computer Vision and Pattern Recognition}, Jun. 2018, pp. 7132--7141.

\bibitem{wooCBAMConvolutionalBlock2018}
S.~Woo, J.~Park, J.-Y. Lee, and I.~S. Kweon, ``Cbam: Convolutional block attention module,'' in \emph{Computer Vision - ECCV 2018}.\hskip 1em plus 0.5em minus 0.4em\relax Springer International Publishing, 2018, pp. 3--19.

\bibitem{carreiraQuoVadisAction2017}
J.~Carreira and A.~Zisserman, ``Quo vadis, action recognition? a new model and the kinetics dataset,'' in \emph{2017 IEEE Conference on Computer Vision and Pattern Recognition (CVPR)}.\hskip 1em plus 0.5em minus 0.4em\relax IEEE, Jul. 2017, pp. 4724--4733.

\end{thebibliography}
