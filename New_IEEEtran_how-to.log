This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024) (preloaded format=pdflatex 2024.8.28)  15 JUL 2025 17:44
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**d:/phd/05MPC论文/MPC/New_IEEEtran_how-to.tex
(d:/phd/05MPC论文/MPC/New_IEEEtran_how-to.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(./IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count188
\@IEEEtrantmpcountB=\count189
\@IEEEtrantmpcountC=\count190
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for OT1+ptm on input line 503.
(d:/texlive/2024/texmf-dist/tex/latex/psnfss/ot1ptm.fd
File: ot1ptm.fd 2001/06/04 font definitions for OT1/ptm.
)
-- Using 8.5in x 11in (letter) paper.
-- Using PDF output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <5> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <5> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <7> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <7> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <8> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <8> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <9> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <9> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <10> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <10> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <11> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <11> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <12> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <12> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/n' in size <18> not available
(Font)              Font shape `OT1/ptm/b/n' tried instead on input line 1086.
LaTeX Font Info:    Font shape `OT1/ptm/bx/it' in size <18> not available
(Font)              Font shape `OT1/ptm/b/it' tried instead on input line 1086.
\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count191
\c@subsection=\count192
\c@subsubsection=\count193
\c@paragraph=\count194
\c@IEEEsubequation=\count195
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count196
\c@table=\count197
\@IEEEeqnnumcols=\count198
\@IEEEeqncolcnt=\count199
\@IEEEsubeqnnumrollback=\count266
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count267
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count268
\@IEEEtranrubishbin=\box52
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen164
)) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen165
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count269
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count270
\leftroot@=\count271
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count272
\DOTSCASE@=\count273
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen166
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count274
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count275
\dotsspace@=\muskip16
\c@parentequation=\count276
\dspbrk@lvl=\count277
\tag@help=\toks19
\row@=\count278
\column@=\count279
\maxfields@=\count280
\andhelp@=\toks20
\eqnshift@=\dimen167
\alignsep@=\dimen168
\tagshift@=\dimen169
\tagwidth@=\dimen170
\totwidth@=\dimen171
\lineht@=\dimen172
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (d:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithmic.sty
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
 (d:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks23
)
\c@ALC@unique=\count281
\c@ALC@line=\count282
\c@ALC@rem=\count283
\c@ALC@depth=\count284
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (d:/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen173
\ar@mcellbox=\box55
\extrarowheight=\dimen174
\NC@list=\toks24
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box56
) (d:/texlive/2024/texmf-dist/tex/latex/subfig/subfig.sty
Package: subfig 2005/06/28 ver: 1.3 subfig package
 (d:/texlive/2024/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen175
\captionmargin=\dimen176
\caption@leftmargin=\dimen177
\caption@rightmargin=\dimen178
\caption@width=\dimen179
\caption@indent=\dimen180
\caption@parindent=\dimen181
\caption@hangindent=\dimen182
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEtablestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\normalfont \footnotesize #1}\\{\normalfont \footnotesize \scshape #2}\par \addvspace {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptionsepspace \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace }\parbox [t]{\hsize }{\normalfont \footnotesize \noindent \unhbox \@tempboxa #2}\else \ifCLASSOPTIONconference \hbox to\hsize {\normalfont \footnotesize \hfil \box \@tempboxa \hfil }\else \hbox to\hsize {\normalfont \footnotesize \box \@tempboxa \hfil }\fi \fi \fi  on input line 1175.
)
\c@KVtest=\count285
\sf@farskip=\skip58
\sf@captopadj=\dimen183
\sf@capskip=\skip59
\sf@nearskip=\skip60
\c@subfigure=\count286
\c@subfigure@save=\count287
\c@lofdepth=\count288
\c@subtable=\count289
\c@subtable@save=\count290
\c@lotdepth=\count291
\sf@top=\skip61
\sf@bottom=\skip62
) (d:/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
) (d:/texlive/2024/texmf-dist/tex/latex/sttools/stfloats.sty
Package: stfloats 2017/03/27 v3.3 Improve float mechanism and baselineskip settings
\@dblbotnum=\count292
\c@dblbotnumber=\count293
) (d:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
) (d:/texlive/2024/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks25
\verbatim@line=\toks26
\verbatim@in@stream=\read2
) (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
 (d:/texlive/2024/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen184
\Gin@req@width=\dimen185
) (d:/texlive/2024/texmf-dist/tex/latex/preprint/balance.sty
Package: balance 1999/02/23 4.3 (PWD)
\oldvsize=\dimen186
) (d:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count294
\l__pdf_internal_box=\box57
) (./New_IEEEtran_how-to.aux)
\openout1 = `New_IEEEtran_how-to.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 15.
LaTeX Font Info:    ... okay on input line 15.

-- Lines per column: 58 (exact).
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: subfig package v1.3 is loaded.
Package caption Info: End \AtBeginDocument code.
(d:/texlive/2024/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count295
\scratchdimen=\dimen187
\scratchbox=\box58
\nofMPsegments=\count296
\nofMParguments=\count297
\everyMPshowfont=\toks27
\MPscratchCnt=\count298
\MPscratchDim=\dimen188
\MPnumerator=\count299
\makeMPintoPDFobject=\count300
\everyMPtoPDFconversion=\toks28
) (d:/texlive/2024/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 485.
 (d:/texlive/2024/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live
))
LaTeX Font Info:    Trying to load font information for TS1+ptm on input line 37.
 (d:/texlive/2024/texmf-dist/tex/latex/psnfss/ts1ptm.fd
File: ts1ptm.fd 2001/06/04 font definitions for TS1/ptm.
)
Underfull \hbox (badness 1688) in paragraph at lines 37--38
[]\OT1/ptm/m/n/10 Conventional ap-proaches ex-hibit com-ple-men-tary weak-
 []

LaTeX Font Info:    Trying to load font information for U+msa on input line 44.
(d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 44.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) [1{d:/texlive/2024/texmf-var/fonts/map/pdftex/updmap/pdftex.map}{d:/texlive/2024/texmf-dist/fonts/enc/dvips/base/8r.enc}


]

pdfTeX warning: pdflatex.exe (file ./fig1.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<fig1.pdf, id=9, 403.14616pt x 371.9496pt>
File: fig1.pdf Graphic file (type pdf)
<use fig1.pdf>
Package pdftex.def Info: fig1.pdf  used on input line 63.
(pdftex.def)             Requested size: 252.0pt x 232.50197pt.

Underfull \hbox (badness 10000) in paragraph at lines 68--69
[]\OT1/ptm/m/n/10 In MPC, to find an op-ti-mal con-trol se-quence
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--69
\OMS/cmsy/m/n/10 U \OT1/cmr/m/n/10 = [\OML/cmm/m/it/10 u[]; []  ; u[]\OT1/cmr/m/n/10 ][]$ \OT1/ptm/m/n/10 to pre-dict state se-quence
 []


Underfull \hbox (badness 10000) in paragraph at lines 68--69
\OMS/cmsy/m/n/10 X \OT1/cmr/m/n/10 = [\OT1/cmr/bx/n/10 x[]\OML/cmm/m/it/10 ; []  ; \OT1/cmr/bx/n/10 x[]\OT1/cmr/m/n/10 ][]$\OT1/ptm/m/n/10 , then, the op-ti-mal sign
 []


Underfull \hbox (badness 3019) in paragraph at lines 68--69
\OT1/ptm/m/n/10 skele-ton $\OML/cmm/m/it/10 K[]$ \OT1/ptm/m/n/10 is ob-tained. The to-tal loss func-tion is:
 []


Overfull \hbox (1.21231pt too wide) detected at line 95
[]\OT1/cmr/bx/n/10 A \OT1/cmr/m/n/10 = [] \OML/cmm/m/it/10 ;  \OT1/cmr/bx/n/10 B \OT1/cmr/m/n/10 = [] \OML/cmm/m/it/10 ;  \OT1/cmr/bx/n/10 C \OT1/cmr/m/n/10 = []
 []

[2 <./fig1.pdf>] [3]
Underfull \hbox (badness 3168) in paragraph at lines 275--276
\OML/cmm/m/it/10 V[] \OT1/cmr/m/n/10 = \OML/cmm/m/it/10 J []$ \OT1/ptm/m/n/10 in-cludes cost over $\OML/cmm/m/it/10 T$ \OT1/ptm/m/n/10 steps, and
 []


Underfull \hbox (badness 4940) in paragraph at lines 307--308
[]\OT1/ptm/m/n/10 Our ex-per-i-men-tal frame-work lever-ages three di-verse
 []


Underfull \hbox (badness 1874) in paragraph at lines 310--311
\OT1/ptm/m/n/10 method-olog-i-cal paradigms in the field. The SIGN-GRU
 []

[4]

pdfTeX warning: pdflatex.exe (file ./fig2.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<fig2.pdf, id=68, 426.03165pt x 347.73915pt>
File: fig2.pdf Graphic file (type pdf)
<use fig2.pdf>
Package pdftex.def Info: fig2.pdf  used on input line 316.
(pdftex.def)             Requested size: 252.0pt x 205.69492pt.
[5 <./fig2.pdf>]

pdfTeX warning: pdflatex.exe (file ./fig3.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<fig3.pdf, id=89, 541.30232pt x 388.69214pt>
File: fig3.pdf Graphic file (type pdf)
<use fig3.pdf>
Package pdftex.def Info: fig3.pdf  used on input line 369.
(pdftex.def)             Requested size: 252.0pt x 180.95348pt.


pdfTeX warning: pdflatex.exe (file ./fig4.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<fig4.pdf, id=90, 554.79266pt x 445.90588pt>
File: fig4.pdf Graphic file (type pdf)
<use fig4.pdf>
Package pdftex.def Info: fig4.pdf  used on input line 378.
(pdftex.def)             Requested size: 252.0pt x 202.5405pt.

Underfull \vbox (badness 1248) has occurred while \output is active []



pdfTeX warning: pdflatex.exe (file ./fig5.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<fig5.pdf, id=91, 461.92577pt x 422.2977pt>
File: fig5.pdf Graphic file (type pdf)
<use fig5.pdf>
Package pdftex.def Info: fig5.pdf  used on input line 388.
(pdftex.def)             Requested size: 252.0pt x 230.37643pt.
Package textcomp Info: Symbol \textdownarrow not provided by
(textcomp)             font family ptm in TS1 encoding.
(textcomp)             Default family used instead on input line 389.
Package textcomp Info: Symbol \textdownarrow not provided by
(textcomp)             font family ptm in TS1 encoding.
(textcomp)             Default family used instead on input line 389.

Underfull \hbox (badness 8075) in paragraph at lines 394--396
 \OT1/ptm/m/it/10 2) Biome-chan-i-cal Com-pli-ance Eval-u-a-tion: [][] \OT1/ptm/m/n/10 In Fig.6,
 []



pdfTeX warning: pdflatex.exe (file ./fig6.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<fig6.pdf, id=92, 1115.00562pt x 943.48488pt>
File: fig6.pdf Graphic file (type pdf)
<use fig6.pdf>
Package pdftex.def Info: fig6.pdf  used on input line 413.
(pdftex.def)             Requested size: 252.0pt x 213.2251pt.
[6 <./fig3.pdf> <./fig4.pdf

pdfTeX warning: pdflatex.exe (file ./fig4.pdf): PDF inclusion: multiple pdfs with page group included in a single page
>]
Underfull \hbox (badness 1533) in paragraph at lines 429--431
\OT1/ptm/m/n/10 sess-ment scale ad-min-is-tered to 50 Deaf eval-u-a-tors, our
 []



pdfTeX warning: pdflatex.exe (file ./fig7.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<fig7.pdf, id=110, 820.62585pt x 684.39693pt>
File: fig7.pdf Graphic file (type pdf)
<use fig7.pdf>
Package pdftex.def Info: fig7.pdf  used on input line 434.
(pdftex.def)             Requested size: 252.0pt x 210.16623pt.
[7{d:/texlive/2024/texmf-dist/fonts/enc/dvips/cm-super/cm-super-ts1.enc} <./fig5.pdf> <./fig6.pdf

pdfTeX warning: pdflatex.exe (file ./fig6.pdf): PDF inclusion: multiple pdfs with page group included in a single page
>]

pdfTeX warning: pdflatex.exe (file ./fig8.pdf): PDF inclusion: found PDF version <1.7>, but at most version <1.5> allowed
<fig8.pdf, id=126, 617.30624pt x 499.8675pt>
File: fig8.pdf Graphic file (type pdf)
<use fig8.pdf>
Package pdftex.def Info: fig8.pdf  used on input line 445.
(pdftex.def)             Requested size: 252.0pt x 204.0546pt.
Package textcomp Info: Symbol \textrightarrow not provided by
(textcomp)             font family ptm in TS1 encoding.
(textcomp)             Default family used instead on input line 458.
Package textcomp Info: Symbol \textrightarrow not provided by
(textcomp)             font family ptm in TS1 encoding.
(textcomp)             Default family used instead on input line 459.
Package textcomp Info: Symbol \textrightarrow not provided by
(textcomp)             font family ptm in TS1 encoding.
(textcomp)             Default family used instead on input line 459.

Underfull \vbox (badness 1383) has occurred while \output is active []

 [8 <./fig7.pdf> <./fig8.pdf

pdfTeX warning: pdflatex.exe (file ./fig8.pdf): PDF inclusion: multiple pdfs with page group included in a single page
>]
Underfull \hbox (badness 3701) in paragraph at lines 463--468
\OT1/ptm/m/n/10 mance met-rics pre-sented in Ta-ble II demon-strate our
 []

[9]
Underfull \hbox (badness 2469) in paragraph at lines 470--471
[]\OT1/ptm/m/n/10 The Transformer-MPC frame-work es-tab-lishes a novel
 []

[10

] (./New_IEEEtran_how-to.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********
 ) 
Here is how much of TeX's memory you used:
 4571 strings out of 476065
 78209 string characters out of 5792787
 1958190 words of memory out of 5000000
 26615 multiletter control sequences out of 15000+600000
 597000 words of font info for 116 fonts, out of 8000000 for 9000
 18 hyphenation exceptions out of 8191
 57i,16n,65p,1684b,257s stack positions out of 10000i,1000n,20000p,200000b,200000s
<d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmex10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi6.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmmib10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr5.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr6.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmr8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy6.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy7.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/cm/cmsy8.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/amsfonts/symbols/msbm10.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm0800.pfb><d:/texlive/2024/texmf-dist/fonts/type1/public/cm-super/sfrm1000.pfb><d:/texlive/2024/texmf-dist/fonts/type1/urw/times/utmb8a.pfb><d:/texlive/2024/texmf-dist/fonts/type1/urw/times/utmbi8a.pfb><d:/texlive/2024/texmf-dist/fonts/type1/urw/times/utmr8a.pfb><d:/texlive/2024/texmf-dist/fonts/type1/urw/times/utmri8a.pfb>
Output written on New_IEEEtran_how-to.pdf (10 pages, 1558674 bytes).
PDF statistics:
 247 PDF objects out of 1000 (max. 8388607)
 146 compressed objects within 2 object streams
 0 named destinations out of 1000 (max. 500000)
 41 words of extra memory for PDF output out of 10000 (max. 10000000)

