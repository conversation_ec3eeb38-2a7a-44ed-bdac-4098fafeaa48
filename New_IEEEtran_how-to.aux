\relax 
\citation{alyamiReviewing25Years2024a,jiangSurveyChineseSign2024}
\citation{zhangRecentAdvancesDeep2024,HuangVideobased2018,cuiRecurrentConvolutionalNeural2017}
\citation{kollerContinuousSignLanguage2015}
\citation{kollerContinuousSignLanguage2015,cihancamgozSignLanguageTransformers2020}
\citation{liWordlevelDeepSign2020a,chenBiLSTMCTCBased2024}
\citation{zuoImprovingContinuousSign2024,zhuMultiscaleTemporalNetwork2022,ahnSlowfastNetworkContinuous2024}
\citation{huTransRACEncodingMultiscale2022,hanTSRTVDTemporalSuperresolution2020}
\citation{xieMultiscaleLocaltemporalSimilarity2023,minVisualAlignmentConstraint2021a}
\citation{huContinuousSignLanguage2023a,huCorrNetSignLanguage2024a}
\citation{zhouSpatialtemporalMulticueNetwork2022,zuoC2SLRConsistencyenhancedContinuous2022,Alyami_2025_CVPR,dinaiTAPSTemporalAttentionbased2025}
\citation{KOLLER2015108,8578910}
\citation{zhou2021,huSqueezeandexcitationNetworks2018,wooCBAMConvolutionalBlock2018}
\@writefile{toc}{\contentsline {section}{\numberline {I}Introduction}{1}{}\protected@file@percent }
\citation{zhangRecentAdvancesDeep2024,HuangVideobased2018,cuiRecurrentConvolutionalNeural2017,kollerContinuousSignLanguage2015,cihancamgozSignLanguageTransformers2020}
\citation{huContinuousSignLanguage2023a,huCorrNetSignLanguage2024a,zhou2021,huSqueezeandexcitationNetworks2018,wooCBAMConvolutionalBlock2018}
\@writefile{toc}{\contentsline {section}{\numberline {II}Methods}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-A}}Overview}{2}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces The proposed SLG model based on Transformer and MPC. Based on given training data $({K^{r(i)},S^{(i)}}_{i=1}^N)$, a Transformer is trained for SLG. The next, the initial estimated data $\hat  {K}$ is inputted into MPC. In MPC, to find an optimal control sequence $\mathcal  {U}=[u_0,\cdots  ,u_{T-1}]^T$ to predict state sequence $\mathcal  {X}=[\mathbf  {x}_1,\cdots  ,\mathbf  {x}_T]^T$, then, the optimal sign skeleton $K^M$ is obtained. The total loss function is: $\mathcal  {L}=\mathcal  {L}_{KL}+\mathcal  {L}_{Track}+\mathcal  {L}_{Acc}+\mathcal  {L}_{Jerk}$.}}{2}{}\protected@file@percent }
\newlabel{fig1}{{1}{2}{}{}{}}
\newlabel{eq1}{{1}{2}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-B}}Optimization for SLG Based on MPC}{2}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {II-B}1}Sequence Prediction Model}{2}{}\protected@file@percent }
\newlabel{eq2}{{2}{2}{}{}{}}
\newlabel{eq3}{{3}{2}{}{}{}}
\newlabel{eq4}{{4}{3}{}{}{}}
\newlabel{eq5}{{5}{3}{}{}{}}
\newlabel{eq6}{{6}{3}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {II-B}2}Object Function}{3}{}\protected@file@percent }
\newlabel{eq7}{{7}{3}{}{}{}}
\newlabel{eq8}{{8}{3}{}{}{}}
\newlabel{eq9}{{9}{3}{}{}{}}
\newlabel{eq10}{{10}{3}{}{}{}}
\newlabel{eq11}{{11}{3}{}{}{}}
\newlabel{eq12}{{12}{3}{}{}{}}
\newlabel{eq13}{{13}{3}{}{}{}}
\newlabel{eq14}{{14}{3}{}{}{}}
\newlabel{eq15}{{15}{3}{}{}{}}
\newlabel{eq16}{{16}{3}{}{}{}}
\newlabel{eq17}{{17}{3}{}{}{}}
\citation{jiangSurveyChineseSign2024}
\citation{zhangRecentAdvancesDeep2024}
\citation{carreiraQuoVadisAction2017}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {II-C}}System Analysis}{4}{}\protected@file@percent }
\newlabel{eq19}{{19}{4}{}{}{}}
\newlabel{eq20}{{20}{4}{}{}{}}
\newlabel{eq21}{{21}{4}{}{}{}}
\newlabel{eq22}{{22}{4}{}{}{}}
\newlabel{eq23}{{23}{4}{}{}{}}
\newlabel{eq24}{{24}{4}{}{}{}}
\newlabel{eq25}{{25}{4}{}{}{}}
\newlabel{eq26}{{26}{4}{}{}{}}
\newlabel{eq27}{{27}{4}{}{}{}}
\@writefile{toc}{\contentsline {section}{\numberline {III}Experiments}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-A}}Dataset}{4}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-B}}Baseline}{4}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Illustration of the SL skeleton representation. (a) shows the original skeleton in CSL500 with 25 joints, which can be reduced to 14 joints. (b) shows the initially detected skeleton from the RGB image using OpenPose. It contains 57 joints, which can be reduced to 50 joints. (c) shows an example of the skeleton sequence (50 joints, contains hand shape content, and the CSL word is "Future").}}{5}{}\protected@file@percent }
\newlabel{fig2}{{2}{5}{}{}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {\mbox  {III-C}}Quantitative Results}{5}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}1}Recognizability}{5}{}\protected@file@percent }
\newlabel{eq28}{{28}{5}{}{}{}}
\newlabel{eq29}{{29}{5}{}{}{}}
\newlabel{eq30}{{30}{5}{}{}{}}
\newlabel{eq31}{{31}{5}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces WER comparsion results and HS accuracy measured comparsion results.}}{6}{}\protected@file@percent }
\newlabel{fig3}{{3}{6}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces Hand Orientation Error Heatmap (OR). Blue: Low Error, Red: High Error. Biomechanical Constraints: Shoulder abduction: <25Nm, Elbow rotation: <20Nm, Wrist flexion: <15Nm. MPC Model: 32\% reduction in orientation error vs baseline.}}{6}{}\protected@file@percent }
\newlabel{fig4}{{4}{6}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}2}Biomechanical Compliance Evaluation}{6}{}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces The experimental results for Movement Trajectory (MOV). Baseline Model: 8.2 ± 1.3, MPC Model: 5.3 ± 0.9 (↓29\%). MPC Model Features: Jerk minimization, Physical constraints integration, Temporal consistency, Smooth trajectory generation.}}{7}{}\protected@file@percent }
\newlabel{fig5}{{5}{7}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {6}{\ignorespaces Comparative Biomechanical Performance Metrics. For biomechanical compliance evaluation, 4 metrics are utilized, includes: (1) Skeleton Error Rate (SER), (2) Joint Limit Violation (JIV), (3) Peak Jerk (PJ), and (4) Torque Efficiency (TE). }}{7}{}\protected@file@percent }
\newlabel{fig6}{{6}{7}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {7}{\ignorespaces Subjective Evaluation of Temporal Naturalness in SLG. (5-Point Scale: 5=Excellent/Human-like, 4=Good, 3=Acceptable, 2=Poor, 1=Unusable). }}{8}{}\protected@file@percent }
\newlabel{fig7}{{7}{8}{}{}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {8}{\ignorespaces Quantitative Analysis of Temporal Dynamics - Velocity Profiles (Top) and Jerk Profiles (Bottom) Across Sign Transitions. }}{8}{}\protected@file@percent }
\newlabel{fig8}{{8}{8}{}{}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}3}Component Contribution Analysis}{8}{}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {\mbox  {III-C}4}Computational Efficiency}{9}{}\protected@file@percent }
\bibstyle{IEEEtran}
\bibdata{references}
\bibcite{alyamiReviewing25Years2024a}{1}
\bibcite{jiangSurveyChineseSign2024}{2}
\bibcite{zhangRecentAdvancesDeep2024}{3}
\bibcite{HuangVideobased2018}{4}
\bibcite{cuiRecurrentConvolutionalNeural2017}{5}
\bibcite{kollerContinuousSignLanguage2015}{6}
\bibcite{cihancamgozSignLanguageTransformers2020}{7}
\bibcite{liWordlevelDeepSign2020a}{8}
\bibcite{chenBiLSTMCTCBased2024}{9}
\bibcite{zuoImprovingContinuousSign2024}{10}
\bibcite{zhuMultiscaleTemporalNetwork2022}{11}
\bibcite{ahnSlowfastNetworkContinuous2024}{12}
\bibcite{huTransRACEncodingMultiscale2022}{13}
\bibcite{hanTSRTVDTemporalSuperresolution2020}{14}
\bibcite{xieMultiscaleLocaltemporalSimilarity2023}{15}
\bibcite{minVisualAlignmentConstraint2021a}{16}
\bibcite{huContinuousSignLanguage2023a}{17}
\bibcite{huCorrNetSignLanguage2024a}{18}
\bibcite{zhouSpatialtemporalMulticueNetwork2022}{19}
\bibcite{zuoC2SLRConsistencyenhancedContinuous2022}{20}
\bibcite{Alyami_2025_CVPR}{21}
\bibcite{dinaiTAPSTemporalAttentionbased2025}{22}
\bibcite{KOLLER2015108}{23}
\bibcite{8578910}{24}
\bibcite{zhou2021}{25}
\bibcite{huSqueezeandexcitationNetworks2018}{26}
\bibcite{wooCBAMConvolutionalBlock2018}{27}
\bibcite{carreiraQuoVadisAction2017}{28}
\@writefile{toc}{\contentsline {section}{\numberline {IV}Conclusion}{10}{}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{References}{10}{}\protected@file@percent }
\gdef \@abspage@last{11}
